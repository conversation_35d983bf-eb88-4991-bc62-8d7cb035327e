package com.nq.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.nq.pojo.UserSignature;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.Map;


public interface UserSignatureMapper extends BaseMapper<UserSignature> {
    int insert(Integer userId, String signatureMsg, Date signatureDate, Integer type);

    @Select("select * from user_signature where user_id=#{userId}")
    Map<String, Object> selectByUserId(@Param("userId") Integer userId);
}
