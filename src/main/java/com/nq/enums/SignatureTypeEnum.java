package com.nq.enums;

import lombok.Getter;

/**
 * 签名类型枚举
 */
@Getter
public enum SignatureTypeEnum {

    /**
     * 身份证签名
     */
    ID_CARD("ID_CARD", "身份证签名"),

    /**
     * 银行卡签名
     */
    BANK_CARD("BANK_CARD", "银行卡签名"),

    /**
     * 协议签名
     */
    AGREEMENT("AGREEMENT", "协议签名"),

    /**
     * 风险确认书签名
     */
    RISK_CONFIRMATION("RISK_CONFIRMATION", "风险确认书签名"),

    /**
     * 其他签名
     */
    OTHER("OTHER", "其他签名");

    /**
     * 类型编码
     */
    private final String code;

    /**
     * 类型描述
     */
    private final String description;

    SignatureTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取枚举
     * @param code 编码
     * @return 枚举实例
     */
    public static SignatureTypeEnum getByCode(String code) {
        for (SignatureTypeEnum type : SignatureTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断编码是否有效
     * @param code 编码
     * @return 是否有效
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
}
