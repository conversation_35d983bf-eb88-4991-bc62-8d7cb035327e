package com.nq.controller.app.user;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Maps;
import com.nq.common.ServerResponse;
import com.nq.common.crypto.Decrypt;
import com.nq.common.crypto.Encrypt;
import com.nq.controller.app.user.vo.info.UserIdReq;
import com.nq.controller.app.user.vo.sign.UploadSignReq;
import com.nq.pojo.UserSignature;
import com.nq.service.IUserSignatureService;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/3/10 15:52
 */

@RestController
@RequestMapping("/api/user/")
public class UserSignController {

    @Autowired
    IUserSignatureService iUserSignatureService;

    // 用户签名回显
    @RequestMapping({"getSignature.do"})
    @Encrypt
    @Decrypt
    public ServerResponse getSignature(HttpServletRequest request, @RequestBody UserIdReq req) {
        List<UserSignature> signatureList = iUserSignatureService.findByUserId(req.getUserId());
        if (CollectionUtil.isEmpty(signatureList)) {
            return ServerResponse.createBySuccess();
        }

        // 为每个签名添加类型描述
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<Map<String, Object>> resultList = signatureList.stream().map(signature -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put("id", signature.getId());
            map.put("user_id", signature.getUserId());
            map.put("signature_msg", signature.getSignatureMsg());
            map.put("signature_date", formatter.format(signature.getSignatureDate()));
            map.put("type", signature.getType());

            // 添加类型描述
            SignatureTypeEnum signatureType = SignatureTypeEnum.getByCode(signature.getType());
            map.put("type_description", signatureType != null ? signatureType.getDescription() : signature.getType());

            return map;
        }).collect(Collectors.toList());

        return ServerResponse.createBySuccess(resultList);
    }

    @RequestMapping({"uploadSign.do"})
    @Encrypt
    @Decrypt
    public ServerResponse uploadSign(@RequestBody UploadSignReq req) {
        // 验证签名类型
        if (StrUtil.isBlank(req.getType())) {
            return ServerResponse.createByErrorMsg("签名类型不能为空！");
        }
        if (!SignatureTypeEnum.isValidCode(req.getType())) {
            return ServerResponse.createByErrorMsg("无效的签名类型！");
        }

        // 检查是否已存在相同类型的签名
        List<UserSignature> existingSignatures = iUserSignatureService.findByUserId(req.getUserId());
        if (CollectionUtil.isNotEmpty(existingSignatures)) {
            boolean typeExists = existingSignatures.stream()
                    .anyMatch(signature -> req.getType().equals(signature.getType()));
            if (typeExists) {
                SignatureTypeEnum signatureType = SignatureTypeEnum.getByCode(req.getType());
                String typeName = signatureType != null ? signatureType.getDescription() : req.getType();
                return ServerResponse.createByErrorMsg("用户已存在" + typeName + "，请勿重复上传！");
            }
        }
        if (StrUtil.isBlank(req.getImgUrl())) {
            return ServerResponse.createByErrorMsg("图片地址为空，请重试！");
        }
        Map<String, Object> fileMap = Maps.newHashMap();
        fileMap.put("url", req.getImgUrl());
        fileMap.put("time", new Date());
        fileMap.put("type", req.getType());
        iUserSignatureService.insert(req.getUserId(), req.getImgUrl(), new Date(), req.getType());
        return ServerResponse.createBySuccess(fileMap);
    }

    // 获取所有签名类型
    @RequestMapping({"getSignatureTypes.do"})
    @Encrypt
    public ServerResponse getSignatureTypes() {
        List<Map<String, Object>> typeList = Arrays.stream(SignatureTypeEnum.values())
                .map(type -> {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("code", type.getCode());
                    map.put("description", type.getDescription());
                    return map;
                }).collect(Collectors.toList());

        return ServerResponse.createBySuccess(typeList);
    }
}
