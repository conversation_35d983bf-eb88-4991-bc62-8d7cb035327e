package com.nq.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.nq.dao.UserSignatureMapper;
import com.nq.pojo.UserSignature;
import com.nq.service.IUserSignatureService;

@Service("IUserSignatureService")
public class UserSignatureServiceImpl implements IUserSignatureService {
    @Autowired
    UserSignatureMapper userSignatureMapper;

    @Override
    public int insert(Integer userId, String signatureMsg, Date signatureDate, Integer type) {
        UserSignature userSignature = new UserSignature();
        userSignature.setUserId(userId);
        userSignature.setSignatureMsg(signatureMsg);
        userSignature.setSignatureDate(signatureDate);
        userSignature.setType(type);
        return userSignatureMapper.insert(userId, signatureMsg, signatureDate, type);

    }

    @Override
    public Map<String, Object> selectByUserId(Integer id) {
        return userSignatureMapper.selectByUserId(id);
    }

    @Override
    public int deleteByUserId(Integer userId) {
        return userSignatureMapper.delete(new LambdaQueryWrapper<UserSignature>().eq(UserSignature::getUserId, userId));
    }

    @Override
    public List<UserSignature> findByUserIdList(List<Integer> userIdList) {
        return userSignatureMapper.selectList(new LambdaQueryWrapper<UserSignature>()
            .in(UserSignature::getUserId, userIdList).orderByAsc(UserSignature::getId));
    }

    @Override
    public List<UserSignature> findByUserId(Integer userId) {
        return userSignatureMapper
            .selectList(new LambdaQueryWrapper<UserSignature>().eq(UserSignature::getUserId, userId));
    }

}
