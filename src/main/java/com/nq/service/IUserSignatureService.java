package com.nq.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.nq.pojo.UserSignature;

public interface IUserSignatureService {
    int insert(Integer userId, String signatureMsg, Date signatureDate, String type);

    Map<String, Object> selectByUserId(Integer id);

    int deleteByUserId(Integer userId);

    List<UserSignature> findByUserIdList(List<Integer> userIdList);

    List<UserSignature> findByUserId(Integer userId);
}
