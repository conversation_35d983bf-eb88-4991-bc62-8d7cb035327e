<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.nq.dao.UserSignatureMapper">
    <insert id="insert"  parameterType="com.nq.pojo.UserSignature">
        insert into user_signature(user_id,signature_msg,signature_date,type)
        values (#{userId,jdbcType=INTEGER}, #{signatureMsg,jdbcType=VARCHAR},#{signatureDate,jdbcType=TIMESTAMP},#{type,jdbcType=VARCHAR})
    </insert>
<!--    <select id="selectByUserId" parameterType="Integer" resultMap="java.util.Map">-->
<!--        select * from user_signature where user_id=#{userId}-->
<!--    </select>-->
</mapper>